'use client';

import { EmptyMaintenanceState } from '@/features/maintenance-logs/components/EmptyMaintenanceState';
import { MaintenanceLogsPagination } from '@/features/maintenance-logs/components/MaintenanceLogsPagination';
import { MaintenanceLogsTable } from '@/features/maintenance-logs/components/MaintenanceLogsTable';
import { MaintenancePageHeader } from '@/features/maintenance-logs/components/MaintenancePageHeader';
import { MaintenancePageSkeleton } from '@/features/maintenance-logs/components/MaintenancePageSkeleton';
import { MinimalistFilters } from '@/features/maintenance-logs/components/MinimalistFilters';
import { StatusCard } from '@/features/maintenance-logs/components/StatusCard';
import { useMaintenanceLogs } from '@/features/maintenance-logs/hooks';
import type { MaintenanceTableState } from '@/features/maintenance-logs/types/table';
import {
  ALL_COLUMNS,
  DEFAULT_TABLE_STATE,
} from '@/features/maintenance-logs/types/table';
import { useProject } from '@/features/projects/hooks/use-projects';
import { useProjectContext } from '@/providers/project-context';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function MaintenanceLogsPage() {
  const router = useRouter();
  const [tableState, setTableState] =
    useState<MaintenanceTableState>(DEFAULT_TABLE_STATE);
  const { isInProjectContext, selectedProjectId, isLoading: projectContextLoading } = useProjectContext();

  // Fetch project data using the selectedProjectId
  const { data: selectedProject } = useProject(selectedProjectId || '');

  // Use the new TanStack Query hook
  const {
    data: logsResponse,
    isLoading,
    error,
  } = useMaintenanceLogs({
    filters: tableState.filters,
    pageIndex: tableState.pageIndex,
    pageSize: tableState.pageSize,
    sorting: tableState.sorting,
  });

  useEffect(() => {
    // Only redirect if project context has finished loading and user is not in project context
    if (!projectContextLoading && !isInProjectContext) {
      router.push('/projects');
      return;
    }
  }, [isInProjectContext, projectContextLoading, router]);

  // Show loading while project context is loading
  if (projectContextLoading) {
    return <MaintenancePageSkeleton />;
  }

  // Redirect if not in project context after loading
  if (!isInProjectContext) {
    return null;
  }

  // Handle loading and error states
  if (error) {
    console.error('Error loading maintenance logs:', error);
  }

  const logs = logsResponse?.data || [];
  const totalItems = logsResponse?.totalCount || 0;

  // Enhanced loading state
  if (isLoading) {
    return <MaintenancePageSkeleton />;
  }

  // Enhanced error state
  if (error && !isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <MaintenancePageHeader projectName={selectedProject?.name} />
        <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-12 h-12 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Something went wrong
              </h1>
              <p className="text-lg text-gray-600 mb-8">
                We encountered an error while loading your maintenance logs.
                Please try again.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-6 py-3 bg-gray-900 text-white font-medium rounded-lg hover:bg-gray-800 transition-colors"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Try Again
              </button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50/50 via-white to-blue-50/30">
      {/* Header */}
      <MaintenancePageHeader projectName={selectedProject?.name} />

      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Hero Section */}
        <div className="mb-8 sm:mb-12">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 tracking-tight">
              Maintenance Dashboard
            </h1>
            <p className="text-gray-600 mt-2 sm:mt-3 text-sm sm:text-base lg:text-lg max-w-2xl">
              Monitor your equipment status and maintenance activities in
              real-time
            </p>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12">
          <StatusCard
            title="Fully Functional"
            value={logs.filter((log) => log.status === 'fully function').length}
            description="Equipment running smoothly"
            icon={
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            }
            variant="success"
          />

          <StatusCard
            title="Partially Working"
            value={
              logs.filter((log) => log.status === 'partially function').length
            }
            description="Needs attention"
            icon={
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            }
            variant="warning"
          />

          <StatusCard
            title="Broken/Issues"
            value={logs.filter((log) => log.status === 'broken').length}
            description="Requires immediate action"
            icon={
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            }
            variant="default"
          />
        </div>

        {/* Data Section */}
        <section className="space-y-6 sm:space-y-8">
          {/* Section Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">
                Maintenance Records
              </h2>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                {totalItems > 0
                  ? `${totalItems.toLocaleString()} records found`
                  : 'No records available'}
              </p>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-3">
              <button
                className="inline-flex items-center px-4 py-2.5 text-sm font-medium text-primary-foreground bg-primary rounded-xl hover:bg-primary/90 transition-all duration-200 shadow-sm hover:shadow-lg"
                onClick={() => router.push('/maintenance-logs/create')}
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Record
              </button>
            </div>
          </div>

          {/* Enhanced Filters Card */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 shadow-sm hover:shadow-md transition-all duration-200">
            <div className="p-6 sm:p-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2.5 bg-gray-100 rounded-xl">
                  <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                    Search & Filter
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-500 mt-0.5">
                    Find specific maintenance records
                  </p>
                </div>
              </div>

              <MinimalistFilters
                filters={tableState.filters}
                onFilterChange={(filters) =>
                  setTableState((prev) => ({ ...prev, filters }))
                }
                tableState={tableState}
                onTableStateChange={setTableState}
                isLoading={isLoading}
              />
            </div>
          </div>

          {/* Enhanced Table Container */}
          {totalItems === 0 && !isLoading ? (
            <EmptyMaintenanceState
              hasFilters={Object.values(tableState.filters).some(Boolean)}
              onClearFilters={() => setTableState(DEFAULT_TABLE_STATE)}
            />
          ) : (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
              {/* Table Header */}
              <div className="px-6 sm:px-8 py-4 sm:py-6 border-b border-gray-200/60 bg-gradient-to-r from-gray-50/50 to-gray-100/30">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2.5 bg-white rounded-xl shadow-sm ring-1 ring-gray-200/50">
                      <svg
                        className="w-5 h-5 text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                        Data Table
                      </h3>
                      <p className="text-xs sm:text-sm text-gray-500 mt-0.5">
                        {totalItems > 0
                          ? `${totalItems.toLocaleString()} maintenance records`
                          : 'No data available'}
                      </p>
                    </div>
                  </div>

                  {totalItems > 0 && (
                    <div className="flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg shadow-sm ring-1 ring-gray-200/50">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs sm:text-sm text-gray-600 font-medium">
                        Page {tableState.pageIndex + 1} of{' '}
                        {Math.ceil(totalItems / tableState.pageSize)}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Table Content */}
              <div className="overflow-x-auto">
                <MaintenanceLogsTable
                  data={logs}
                  columns={ALL_COLUMNS}
                  tableState={tableState}
                  onTableStateChange={setTableState}
                  isLoading={isLoading}
                  totalItems={totalItems}
                />
              </div>

              {/* Enhanced Pagination */}
              {totalItems > 0 && (
                <div className="px-6 sm:px-8 py-4 sm:py-6 border-t border-gray-200/60 bg-gradient-to-r from-gray-50/30 to-white">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="text-sm text-gray-600 order-2 sm:order-1">
                      Showing{' '}
                      <span className="font-semibold text-gray-900">
                        {tableState.pageIndex * tableState.pageSize + 1}
                      </span>{' '}
                      to{' '}
                      <span className="font-semibold text-gray-900">
                        {Math.min(
                          (tableState.pageIndex + 1) * tableState.pageSize,
                          totalItems,
                        )}
                      </span>{' '}
                      of{' '}
                      <span className="font-semibold text-gray-900">
                        {totalItems.toLocaleString()}
                      </span>{' '}
                      results
                    </div>
                    <div className="order-1 sm:order-2">
                      <MaintenanceLogsPagination
                        currentPage={tableState.pageIndex + 1}
                        totalPages={Math.ceil(totalItems / tableState.pageSize)}
                        onPageChange={(page) =>
                          setTableState((prev) => ({
                            ...prev,
                            pageIndex: page - 1,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </section>
      </main>
    </div>
  );
}
