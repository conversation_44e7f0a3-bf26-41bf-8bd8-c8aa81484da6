'use client';

import { useProjectContext } from '@/providers/project-context';
import { useProject } from '@/features/projects/hooks/use-projects';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { 
  Wrench, 
  MessageSquare, 
  FileText, 
  Users, 
  BarChart3,
  Calendar,
  MapPin,
  Building2
} from 'lucide-react';

export default function ProjectDashboardPage() {
  const { selectedProjectId } = useProjectContext();
  const { data: project } = useProject(selectedProjectId || '');
  const router = useRouter();

  const quickActions = [
    {
      title: 'Maintenance Logs',
      description: 'View and manage maintenance activities',
      icon: Wrench,
      href: `/projects/${selectedProjectId}/maintenance-logs`,
      color: 'bg-blue-500',
    },
    {
      title: 'Complaints',
      description: 'Track damage complaints and repairs',
      icon: MessageSquare,
      href: `/projects/${selectedProjectId}/complaints`,
      color: 'bg-orange-500',
    },
    {
      title: 'PMA Certificates',
      description: 'Manage PMA certificates',
      icon: FileText,
      href: `/projects/${selectedProjectId}/pmas`,
      color: 'bg-green-500',
    },
    {
      title: 'Team Members',
      description: 'Manage project team',
      icon: Users,
      href: `/projects/${selectedProjectId}/members`,
      color: 'bg-purple-500',
    },
  ];

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading project...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Hero Section */}
        <div className="mb-8 sm:mb-12">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 tracking-tight">
              Project Dashboard
            </h1>
            <p className="text-gray-600 mt-2 sm:mt-3 text-sm sm:text-base lg:text-lg max-w-2xl">
              Overview and quick access to all project activities
            </p>
          </div>
        </div>

        {/* Project Info Card */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Building2 className="h-6 w-6 text-primary" />
              Project Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Location</p>
                  <p className="font-medium">{project.location}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Start Date</p>
                  <p className="font-medium">
                    {project.start_date ? new Date(project.start_date).toLocaleDateString() : 'Not set'}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <BarChart3 className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <p className="font-medium capitalize">{project.status}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action) => (
            <Card 
              key={action.title}
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => router.push(action.href)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${action.color} text-white`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-lg">{action.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm">{action.description}</p>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="mt-3 p-0 h-auto text-primary hover:text-primary"
                >
                  View →
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
