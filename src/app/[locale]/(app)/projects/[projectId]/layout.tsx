'use client';

import { useProject } from '@/features/projects/hooks/use-projects';
import { useProjectContext } from '@/providers/project-context';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';

interface ProjectLayoutProps {
  children: React.ReactNode;
  params: Promise<{ projectId: string }>;
}

export default function ProjectLayout({ children, params }: ProjectLayoutProps) {
  const router = useRouter();
  const { selectProject, selectedProjectId } = useProjectContext();

  // Unwrap params
  const [projectId, setProjectId] = React.useState<string | null>(null);

  React.useEffect(() => {
    params.then(({ projectId }) => {
      setProjectId(projectId);
    });
  }, [params]);

  const { data: project, isLoading, error } = useProject(projectId || '');

  // Set project context when projectId changes
  useEffect(() => {
    if (projectId && projectId !== selectedProjectId) {
      selectProject(projectId);
    }
  }, [projectId, selectedProjectId, selectProject]);

  // Redirect if project not found
  useEffect(() => {
    if (!isLoading && (error || !project) && projectId) {
      router.push('/projects');
    }
  }, [isLoading, error, project, projectId, router]);

  if (!projectId || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-2">
          <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
          <div className="text-lg">Loading project...</div>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return null; // Will redirect
  }

  return (
    <div>
      {/* Optional: Project header/breadcrumb */}
      <div className="border-b bg-white/80 backdrop-blur-sm px-6 py-4 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold text-gray-900">{project.name}</h1>
            <p className="text-sm text-gray-600">{project.code}</p>
          </div>
          <div className="text-xs text-gray-500">
            Project ID: {projectId}
          </div>
        </div>
      </div>
      {children}
    </div>
  );
}
