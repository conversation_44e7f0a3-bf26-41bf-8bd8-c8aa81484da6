import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useProjectContext } from '@/providers/project-context';
import { FileText, Plus, Search, Settings } from 'lucide-react';
import Link from 'next/link';

interface EmptyMaintenanceStateProps {
  hasFilters?: boolean;
  onClearFilters?: () => void;
}

export function EmptyMaintenanceState({
  hasFilters = false,
  onClearFilters,
}: EmptyMaintenanceStateProps) {
  const { selectedProjectId } = useProjectContext();
  if (hasFilters) {
    // Empty state when filters are applied
    return (
      <Card className="border border-gray-200 bg-white">
        <div className="flex flex-col items-center justify-center py-16 px-8 text-center">
          {/* Simple search icon */}
          <div className="mb-6">
            <div className="p-4 bg-gray-100 rounded-lg">
              <Search className="h-12 w-12 text-gray-600" />
            </div>
          </div>

          <h3 className="text-xl font-medium text-gray-900 mb-3">
            No matching records found
          </h3>
          <p className="text-sm text-gray-600 max-w-md mb-6">
            We couldn&apos;t find any maintenance logs that match your current
            filters. Try adjusting your search criteria or clearing the filters.
          </p>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            {onClearFilters && (
              <Button
                onClick={onClearFilters}
                variant="outline"
                className="px-4 py-2 rounded-lg border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <Search className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            )}
            <Button
              asChild
              className="px-4 py-2 bg-gray-900 hover:bg-gray-800 rounded-lg"
            >
              <Link href={`/projects/${selectedProjectId}/maintenance-logs/create`}>
                <Plus className="h-4 w-4 mr-2" />
                Add New Record
              </Link>
            </Button>
          </div>

          {/* Helpful tips */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h4 className="text-sm font-medium text-gray-800 mb-2">
              Search Tips:
            </h4>
            <ul className="text-xs text-gray-600 space-y-1 text-left">
              <li>• Try using different keywords or phrases</li>
              <li>• Check your date range selection</li>
              <li>• Remove some filters to broaden your search</li>
              <li>• Verify the status and operation type filters</li>
            </ul>
          </div>
        </div>
      </Card>
    );
  }

  // Empty state when no data exists at all
  return (
    <Card className="border border-gray-200 bg-white">
      <div className="flex flex-col items-center justify-center py-20 px-8 text-center">
        {/* Simple icon */}
        <div className="mb-8">
          <div className="p-6 bg-gray-100 rounded-lg">
            <FileText className="h-16 w-16 text-gray-600" />
          </div>
        </div>

        <h3 className="text-2xl font-medium text-gray-900 mb-4">
          Welcome to Maintenance Logs
        </h3>
        <p className="text-sm text-gray-600 max-w-lg mb-8">
          Start tracking your maintenance activities by creating your first log
          entry. Monitor equipment status, schedule maintenance, and keep
          detailed records all in one place.
        </p>

        {/* Primary action */}
        <div className="mb-10">
          <Button
            asChild
            size="lg"
            className="px-6 py-3 font-medium bg-gray-900 hover:bg-gray-800 rounded-lg"
          >
            <Link href={`/projects/${selectedProjectId}/maintenance-logs/create`}>
              <Plus className="h-5 w-5 mr-2" />
              Create Your First Log
            </Link>
          </Button>
        </div>

        {/* Feature highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl">
          <div className="text-center p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="w-10 h-10 mx-auto mb-3 bg-gray-100 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-gray-600" />
            </div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Track Activities
            </h4>
            <p className="text-xs text-gray-600">
              Record daily logs, scheduled maintenance, and emergency repairs
              with detailed information.
            </p>
          </div>

          <div className="text-center p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="w-10 h-10 mx-auto mb-3 bg-gray-100 rounded-lg flex items-center justify-center">
              <Search className="h-5 w-5 text-gray-600" />
            </div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Smart Filtering
            </h4>
            <p className="text-xs text-gray-600">
              Easily find specific records with powerful search and filtering
              capabilities.
            </p>
          </div>

          <div className="text-center p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="w-10 h-10 mx-auto mb-3 bg-gray-100 rounded-lg flex items-center justify-center">
              <Settings className="h-5 w-5 text-gray-600" />
            </div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              System Monitoring
            </h4>
            <p className="text-xs text-gray-600">
              Monitor equipment status and get insights into maintenance
              patterns and trends.
            </p>
          </div>
        </div>

        {/* Secondary actions */}
        <div className="mt-8 flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            className="px-4 py-2 rounded-lg border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            <FileText className="h-4 w-4 mr-2" />
            View Templates
          </Button>
          <Button
            variant="outline"
            className="px-4 py-2 rounded-lg border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            <Settings className="h-4 w-4 mr-2" />
            Import Data
          </Button>
        </div>
      </div>
    </Card>
  );
}
