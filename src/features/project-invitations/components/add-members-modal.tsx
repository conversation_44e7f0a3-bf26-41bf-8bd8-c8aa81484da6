'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertCircle,
  CheckCircle2,
  Mail,
  Plus,
  Trash2,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useInviteExistingUsers } from '../hooks/use-invite-existing-users';
import type { InvitationMember } from '../types/invitation';

interface AddMembersModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

/**
 * Modal component for adding new members to a project
 * Handles both existing and non-existing users (Phase 1: existing users only)
 */
export function AddMembersModal({
  open,
  onOpenChange,
  onSuccess,
}: AddMembersModalProps) {
  const t = useTranslations('pages.members');
  const [members, setMembers] = useState<InvitationMember[]>([
    { email: '', role: 'technician' as const },
  ]);
  const [memberStatuses, setMemberStatuses] = useState<
    Record<
      number,
      {
        status:
          | 'success'
          | 'already_added'
          | 'user_not_found'
          | 'invited'
          | 'error'
          | null;
        message: string;
      }
    >
  >({});

  const inviteExistingUsers = useInviteExistingUsers();

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!open) {
      setMembers([{ email: '', role: 'technician' as const }]);
      setMemberStatuses({});
    }
  }, [open]);

  const addMemberRow = () => {
    setMembers([...members, { email: '', role: 'technician' as const }]);
  };

  const removeMemberRow = (index: number) => {
    if (members.length > 1) {
      const newMembers = members.filter((_, i) => i !== index);
      setMembers(newMembers);

      // Clear status for removed member
      const newStatuses = { ...memberStatuses };
      delete newStatuses[index];

      // Reindex remaining statuses
      const reindexedStatuses: typeof memberStatuses = {};
      Object.keys(newStatuses).forEach((key) => {
        const numKey = parseInt(key);
        if (numKey > index) {
          reindexedStatuses[numKey - 1] = newStatuses[numKey];
        } else {
          reindexedStatuses[numKey] = newStatuses[numKey];
        }
      });
      setMemberStatuses(reindexedStatuses);
    }
  };

  const updateMember = (
    index: number,
    field: 'email' | 'role',
    value: string,
  ) => {
    const updatedMembers = [...members];
    if (field === 'email') {
      updatedMembers[index][field] = value;
    } else if (field === 'role') {
      updatedMembers[index][field] = 'technician' as const; // Fixed role for Phase 1
    }
    setMembers(updatedMembers);

    // Clear status when email changes
    if (field === 'email') {
      const newStatuses = { ...memberStatuses };
      delete newStatuses[index];
      setMemberStatuses(newStatuses);
    }
  };

  const handleAddMembers = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that all members have email
    const validMembers = members.filter((member) => member.email.trim());

    if (validMembers.length === 0) {
      return;
    }

    try {
      const result = await inviteExistingUsers.mutateAsync(validMembers);

      // Update individual member statuses
      const newStatuses: typeof memberStatuses = {};
      result.results.forEach((inviteResult) => {
        const memberIndex = members.findIndex(
          (m) => m.email === inviteResult.email,
        );
        if (memberIndex !== -1) {
          newStatuses[memberIndex] = {
            status: inviteResult.status,
            message: inviteResult.message,
          };
        }
      });

      setMemberStatuses(newStatuses);

      // Reset form if all successful (added or invited) or show results
      if (
        result.errorCount === 0 &&
        result.userNotFoundCount === 0 &&
        result.alreadyAddedCount === 0 &&
        (result.successCount > 0 || result.invitedCount > 0)
      ) {
        // All successful - reset form and close modal
        setMembers([{ email: '', role: 'technician' as const }]);
        setMemberStatuses({});
        onOpenChange(false);
        onSuccess?.();
      }
    } catch (error) {
      console.error('Error inviting members:', error);
    }
  };

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'already_added':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'user_not_found':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'invited':
        return <Mail className="h-4 w-4 text-blue-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{t('modal.title')}</DialogTitle>
          <DialogDescription>{t('modal.description')}</DialogDescription>
        </DialogHeader>

        <form
          onSubmit={handleAddMembers}
          className="flex flex-col flex-1 min-h-0"
        >
          <div className="flex-1 overflow-y-auto space-y-4 pr-2 -mr-2">
            {members.map((member, index) => (
              <div key={index} className="space-y-4 p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Member {index + 1}</h4>
                  {members.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMemberRow(index)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">{t('modal.removeMember')}</span>
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`email-${index}`}>
                      {t('modal.email.label')}
                    </Label>
                    <div className="relative">
                      <Input
                        id={`email-${index}`}
                        type="email"
                        placeholder={t('modal.email.placeholder')}
                        value={member.email}
                        onChange={(e) =>
                          updateMember(index, 'email', e.target.value)
                        }
                        required
                        className={
                          memberStatuses[index]?.status === 'error' ||
                          memberStatuses[index]?.status === 'user_not_found'
                            ? 'border-red-500'
                            : ''
                        }
                      />
                      {memberStatuses[index] && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          {getStatusIcon(memberStatuses[index].status)}
                        </div>
                      )}
                    </div>
                    {memberStatuses[index] && (
                      <div
                        className={`text-xs mt-1 ${
                          memberStatuses[index].status === 'success'
                            ? 'text-green-600'
                            : memberStatuses[index].status === 'already_added'
                              ? 'text-yellow-600'
                              : memberStatuses[index].status === 'invited'
                                ? 'text-blue-600'
                                : 'text-red-600'
                        }`}
                      >
                        {memberStatuses[index].message}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`role-${index}`}>
                      {t('modal.role.label')}
                    </Label>
                    <Select
                      value={member.role}
                      onValueChange={(value) =>
                        updateMember(index, 'role', value)
                      }
                      required
                    >
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t('modal.role.placeholder')}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technician">
                          {t('modal.role.options.technician')}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={addMemberRow}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t('modal.addAnother')}
            </Button>
          </div>

          <div className="flex justify-end space-x-2 pt-4 border-t bg-background flex-shrink-0">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={inviteExistingUsers.isPending}
            >
              {t('modal.actions.cancel')}
            </Button>
            <Button type="submit" disabled={inviteExistingUsers.isPending}>
              {inviteExistingUsers.isPending
                ? t('modal.actions.adding')
                : t('modal.actions.add')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
