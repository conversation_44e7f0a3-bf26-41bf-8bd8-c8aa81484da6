'use client';

import { createContext, ReactNode, useContext, useEffect, useState } from 'react';

interface ProjectContextValue {
  selectedProjectId: string | null;
  selectProject: (projectId: string) => void;
  clearProject: () => void;
  isInProjectContext: boolean;
  isLoading: boolean;
}

const ProjectContext = createContext<ProjectContextValue | undefined>(
  undefined,
);

interface ProjectContextProviderProps {
  children: ReactNode;
}

const PROJECT_STORAGE_KEY = 'simple-selected-project-id';

export function ProjectContextProvider({
  children,
}: ProjectContextProviderProps) {
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);

  // Load project ID from localStorage on mount
  useEffect(() => {
    try {
      const storedProjectId = localStorage.getItem(PROJECT_STORAGE_KEY);
      if (storedProjectId) {
        setSelectedProjectId(storedProjectId);
      }
    } catch (error) {
      console.warn('Failed to load project from localStorage:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const selectProject = (projectId: string) => {
    setSelectedProjectId(projectId);
    try {
      localStorage.setItem(PROJECT_STORAGE_KEY, projectId);
    } catch (error) {
      console.warn('Failed to save project to localStorage:', error);
    }
  };

  const clearProject = () => {
    setSelectedProjectId(null);
    try {
      localStorage.removeItem(PROJECT_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear project from localStorage:', error);
    }
  };

  const isInProjectContext = selectedProjectId !== null;

  return (
    <ProjectContext.Provider
      value={{
        selectedProjectId,
        selectProject,
        clearProject,
        isInProjectContext,
        isLoading,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error(
      'useProjectContext must be used within a ProjectContextProvider',
    );
  }
  return context;
}
